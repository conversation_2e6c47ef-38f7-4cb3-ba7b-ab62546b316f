package com.jinghang.cash.enums;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.jinghang.cash.convert.FileTypeDeserializer;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2023/9/21
 */
@JsonDeserialize(using = FileTypeDeserializer.class)
public enum FileType {
    @JSONField(name = "ID_HEAD")
    ID_HEAD("ID_HEAD","身份证人头面"),
    @JSONField(name = "ID_NATION")
    ID_NATION("ID_NATION","身份证国徽面"),
    @JSONField(name = "ID_FACE")
    ID_FACE("ID_FACE","活体人脸"),
    //    咨询服务合同(超捷).pdf
    CONSULTING_SERVICE_CONTRACT("ID_FACE","咨询服务合同(超捷)"),
    //    个人敏感信息授权书.pdf
    PERSONAL_INFORMATION_AUTHORIZATION_LETTER("PERSONAL_INFORMATION_AUTHORIZATION_LETTER","个人敏感信息授权书"),


    //    综合授权书-担保（四合一）.pdf
    COMPREHENSIVE_AUTHORIZATION_LETTER_GUARANTEE("COMPREHENSIVE_AUTHORIZATION_LETTER_GUARANTEE"," 综合授权书-担保（四合一）"),

    //    委托扣款授权书-担保vs借款人.pdf
    AUTHORIZATION_LETTER_FOR_ENTRUSTED_DEDUCTION_GUARANTEE("AUTHORIZATION_LETTER_FOR_ENTRUSTED_DEDUCTION_GUARANTEE","委托扣款授权书-担保vs借款人"),

    //    综合授权书（通用）.pdf
    COMPREHENSIVE_AUTHORIZATION_LETTER_COMMON("COMPREHENSIVE_AUTHORIZATION_LETTER_COMMON","综合授权书（通用）"),


    //    数字证书使用授权协议.pdf
    DIGITAL_CERTIFICATE_USAGE_AUTHORIZATION_AGREEMENT("DIGITAL_CERTIFICATE_USAGE_AUTHORIZATION_AGREEMENT","个人敏感信息授权书"),

    //    承诺书.pdf
    LETTER_OF_COMMITMENT("LETTER_OF_COMMITMENT","承诺书.pdf"),

//    借款合同及贷款告知事项客户声明书.pdf


    GEOGRAPHICAL_COMMITMENT("GEOGRAPHICAL_COMMITMENT","地域承诺函"),

    LOAN_FILE("LOAN_FILE","放款对账文件"),
    REPAYMENT_FILE("REPAYMENT_FILE","还款对账文件"),
    COMPENSATION_INNER_MARK_FILE("COMPENSATION_INNER_MARK_FILE","代偿标记文件"),
    COMPENSATION_FILE("COMPENSATION_FILE","代偿文件"),
    REPURCHASE_FILE("REPURCHASE_FILE","回购文件"),

    FACE_AUTH_APPROVAL("FACE_AUTH_APPROVAL","人脸认证服务授权书"),
    SENSITIVE_PERSONAL_INFORMATION_HANDLE_LETTER("SENSITIVE_PERSONAL_INFORMATION_HANDLE_LETTER","敏感个人信息处理授权书"),

    BORROWER_IMPORTANT_INFORMATION_TIPS("BORROWER_IMPORTANT_INFORMATION_TIPS","重要提示"),
    DUE_FILE("DUE_FILE","还款计划担保费文件"),
    LOAN_VOUCHER_FILE("LOAN_VOUCHER_FILE","放款凭证文件"),
    DIGITAL_CERTIFICATE_AUTHORIZATION_LETTER("DIGITAL_CERTIFICATE_AUTHORIZATION_LETTER","数字证书授权使用书"),
    DEBT_CONFIRMATION_AGREEMENT("DEBT_CONFIRMATION_AGREEMENT","债权确认协议"),
    PERSONAL_LOAN_CUSTOMER_COMMIT_CONFIRMATION("PERSONAL_LOAN_CUSTOMER_COMMIT_CONFIRMATION","个人贷款客户承诺确认书"),
    CREDIT_SETTLE_VOUCHER_FILE("CREDIT_SETTLE_VOUCHER_FILE","结清证明文件"),
    COLLECTION_AGREEMENT("COLLECTION_AGREEMENT","代收付协议"),
    PERSONAL_LOAN_USE_COMMITMENT("PERSONAL_LOAN_USE_COMMITMENT","个人贷款用途承诺书"),
    SYNTHESIS_AUTHORIZATION("SYNTHESIS_AUTHORIZATION","综合授权书"),

    ENTRUSTED_DEDUCTION_LETTER("ENTRUSTED_DEDUCTION_LETTER","个人客户扣款授权书"),
    LOAN_CONTRACT("LOAN_CONTRACT","借款合同"),
    // 添加征信授权书
    PERSONAL_CREDIT_AUTHORIZATION_LETTER_CREDIT("PERSONAL_CREDIT_AUTHORIZATION_LETTER_CREDIT","征信授权书"),

    // 添加仲裁协议
    ARBITRATION_AGREEMENT("ARBITRATION_AGREEMENT","仲裁协议"),
    ;

    private String desc;
    private String code;

    FileType(String code,String desc) {
        this.desc = desc;
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }


    // 支持多种输入格式的解析方法
    @JsonCreator
    public static FileType fromValue(Object value) {
        if (value == null) {
            return null;
        }
        // 处理字符串输入
        if (value instanceof String) {
            String strValue = ((String) value).trim();

            // 1. 尝试按中文名称解析
            for (FileType type : values()) {
                if (type.getDesc().equals(strValue)) {
                    return type;
                }
            }

            // 2. 尝试按枚举名称解析
            try {
                return FileType.valueOf(strValue.toUpperCase());
            } catch (IllegalArgumentException e) {
                // 继续尝试其他方式
            }

            // 3. 尝试按code解析
            for (FileType type : values()) {
                if (type.getCode().equalsIgnoreCase(strValue)) {
                    return type;
                }
            }
        }

        // 处理JSON对象输入（你遇到的错误情况）
        if (value instanceof java.util.Map) {
            java.util.Map<?, ?> map = (java.util.Map<?, ?>) value;
            Object contractTemplateType = map.get("contractTemplateType");
            if (contractTemplateType instanceof String) {
                return fromDescName((String) contractTemplateType);
            }
        }

        throw new IllegalArgumentException("无法解析 FileType: " + value);
    }

    // 按中文名称解析的辅助方法
    public static FileType fromDescName(String chineseName) {
        return Arrays.stream(values())
                .filter(type -> type.getDesc().equals(chineseName))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("未知的文件类型: " + chineseName));
    }

    // 序列化时输出code
    @JsonValue
    public String toValue() {
        return this.code;
    }
}
